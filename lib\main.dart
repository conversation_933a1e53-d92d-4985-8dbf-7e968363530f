import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'routes/app_pages.dart';
import 'services/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة الخدمات
  await initServices();

  runApp(const MyApp());
}

// تهيئة الخدمات المطلوبة
Future<void> initServices() async {
  // تهيئة خدمة المصادقة
  Get.put(AuthService(), permanent: true);
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'متجر النظارات العصري',
      theme: ThemeData(
        primarySwatch: Colors.teal,
        useMaterial3: true,
        fontFamily: 'Arial', // يمكن تغييرها لخط عربي أفضل
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontSize: 16),
          bodyMedium: TextStyle(fontSize: 14),
        ),
      ),
      initialRoute: '/login', // البدء بشاشة تسجيل الدخول
      getPages: AppPages.routes,
      locale: const Locale('ar', 'SA'), // اللغة العربية
      fallbackLocale: const Locale('en', 'US'),
    );
  }
}
