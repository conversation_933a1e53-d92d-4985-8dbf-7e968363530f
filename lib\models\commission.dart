enum CommissionStatus { pending, approved, paid, cancelled }

class Commission {
  final String id;
  final String affiliateId; // معرف المسوق
  final String customerId; // معرف العميل
  final String orderId; // معرف الطلب
  final String productId; // معرف المنتج
  final double orderAmount; // قيمة الطلب
  final double commissionRate; // نسبة العمولة (0.10 = 10%)
  final double commissionAmount; // مبلغ العمولة
  final CommissionStatus status;
  final DateTime createdAt;
  final DateTime? approvedAt;
  final DateTime? paidAt;
  final String? notes; // ملاحظات إضافية

  Commission({
    required this.id,
    required this.affiliateId,
    required this.customerId,
    required this.orderId,
    required this.productId,
    required this.orderAmount,
    required this.commissionRate,
    required this.commissionAmount,
    this.status = CommissionStatus.pending,
    required this.createdAt,
    this.approvedAt,
    this.paidAt,
    this.notes,
  });

  bool get isPending => status == CommissionStatus.pending;
  bool get isApproved => status == CommissionStatus.approved;
  bool get isPaid => status == CommissionStatus.paid;
  bool get isCancelled => status == CommissionStatus.cancelled;

  String get formattedCommissionAmount => "\$${commissionAmount.toStringAsFixed(2)}";
  String get formattedOrderAmount => "\$${orderAmount.toStringAsFixed(2)}";
  String get commissionPercentage => "${(commissionRate * 100).toStringAsFixed(0)}%";

  // حساب العمولة
  static double calculateCommission(double orderAmount, double rate) {
    return orderAmount * rate;
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'affiliateId': affiliateId,
      'customerId': customerId,
      'orderId': orderId,
      'productId': productId,
      'orderAmount': orderAmount,
      'commissionRate': commissionRate,
      'commissionAmount': commissionAmount,
      'status': status.toString(),
      'createdAt': createdAt.toIso8601String(),
      'approvedAt': approvedAt?.toIso8601String(),
      'paidAt': paidAt?.toIso8601String(),
      'notes': notes,
    };
  }

  // إنشاء من JSON
  factory Commission.fromJson(Map<String, dynamic> json) {
    return Commission(
      id: json['id'] ?? '',
      affiliateId: json['affiliateId'] ?? '',
      customerId: json['customerId'] ?? '',
      orderId: json['orderId'] ?? '',
      productId: json['productId'] ?? '',
      orderAmount: (json['orderAmount'] ?? 0.0).toDouble(),
      commissionRate: (json['commissionRate'] ?? 0.0).toDouble(),
      commissionAmount: (json['commissionAmount'] ?? 0.0).toDouble(),
      status: CommissionStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
        orElse: () => CommissionStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      approvedAt: json['approvedAt'] != null 
          ? DateTime.parse(json['approvedAt']) 
          : null,
      paidAt: json['paidAt'] != null 
          ? DateTime.parse(json['paidAt']) 
          : null,
      notes: json['notes'],
    );
  }

  // نسخ مع تعديل
  Commission copyWith({
    CommissionStatus? status,
    DateTime? approvedAt,
    DateTime? paidAt,
    String? notes,
  }) {
    return Commission(
      id: id,
      affiliateId: affiliateId,
      customerId: customerId,
      orderId: orderId,
      productId: productId,
      orderAmount: orderAmount,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      status: status ?? this.status,
      createdAt: createdAt,
      approvedAt: approvedAt ?? this.approvedAt,
      paidAt: paidAt ?? this.paidAt,
      notes: notes ?? this.notes,
    );
  }
}

// بيانات تجريبية للعمولات
final List<Commission> demoCommissions = [
  Commission(
    id: 'comm_001',
    affiliateId: 'aff_001',
    customerId: 'cust_001',
    orderId: 'order_001',
    productId: '1',
    orderAmount: 299.99,
    commissionRate: 0.10,
    commissionAmount: 29.99,
    status: CommissionStatus.approved,
    createdAt: DateTime.now().subtract(const Duration(days: 5)),
    approvedAt: DateTime.now().subtract(const Duration(days: 2)),
  ),
  Commission(
    id: 'comm_002',
    affiliateId: 'aff_001',
    customerId: 'cust_002',
    orderId: 'order_002',
    productId: '2',
    orderAmount: 199.99,
    commissionRate: 0.10,
    commissionAmount: 19.99,
    status: CommissionStatus.pending,
    createdAt: DateTime.now().subtract(const Duration(days: 1)),
  ),
];
