class Product {
  final int id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final bool isPopular;
  final bool isRecommended;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    this.isPopular = false,
    this.isRecommended = false,
  });

  // Static method to get demo products
  static List<Product> get demoProducts => _demoProducts;

  // Get popular products only
  static List<Product> get popularProducts =>
      _demoProducts.where((product) => product.isPopular).toList();

  // Get recommended products only
  static List<Product> get recommendedProducts =>
      _demoProducts.where((product) => product.isRecommended).toList();

  // Format price as string
  String get formattedPrice => "\$${price.toStringAsFixed(2)}";

  // Check if product is on sale (you can modify this logic)
  bool get isOnSale => isPopular && isRecommended;
}

// قائمة منتجات وهمية لاختبار العرض - نظارات وإكسسوارات
final List<Product> _demoProducts = [
  Product(
    id: 1,
    name: "نظارة شمسية كلاسيكية",
    description: "نظارة شمسية أنيقة بإطار معدني وعدسات مقاومة للأشعة فوق البنفسجية",
    price: 299.99,
    imageUrl: "https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=150&h=150&fit=crop",
    isPopular: true,
    isRecommended: true,
  ),
  Product(
    id: 2,
    name: "نظارة طبية عصرية",
    description: "إطار نظارة طبية مريح ومناسب للاستخدام اليومي",
    price: 199.99,
    imageUrl: "https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=150&h=150&fit=crop",
    isPopular: true,
    isRecommended: false,
  ),
  Product(
    id: 3,
    name: "نظارة رياضية",
    description: "نظارة مصممة خصيصاً للأنشطة الرياضية مع حماية عالية",
    price: 159.99,
    imageUrl: "https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=150&h=150&fit=crop",
    isPopular: false,
    isRecommended: true,
  ),
  Product(
    id: 4,
    name: "نظارة قراءة أنيقة",
    description: "نظارة قراءة مريحة بتصميم كلاسيكي وجودة عالية",
    price: 89.99,
    imageUrl: "https://images.unsplash.com/photo-1473496169904-658ba7c44d8a?w=150&h=150&fit=crop",
    isPopular: true,
    isRecommended: true,
  ),
];
